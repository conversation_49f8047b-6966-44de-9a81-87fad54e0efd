# Profile Components

এই ডিরেক্টরিতে প্রোফাইল পেইজের জন্য আধুনিক এবং পুনঃব্যবহারযোগ্য কম্পোনেন্টগুলি রয়েছে।

## কম্পোনেন্ট তালিকা

### 1. ProfileHeader (`profile_header.dart`)
- কভার ফটো এবং প্রোফাইল ছবি প্রদর্শন
- এডিট বাটন সহ ইন্টারঅ্যাক্টিভ ডিজাইন
- গ্রেডিয়েন্ট ব্যাকগ্রাউন্ড এবং ডেকোরেটিভ এলিমেন্ট

### 2. UserInfoCard (`user_info_card.dart`)
- ইউজারের নাম এবং ইউজারনেম
- ভেরিফিকেশন ব্যাজ
- জয়েনিং ডেট

### 3. StatisticsCard (`statistics_card.dart`)
- ফলোয়ার, ফলোয়িং এবং পোস্ট কাউন্ট
- ক্লিকযোগ্য স্ট্যাট কার্ড
- আইকন সহ আধুনিক ডিজাইন

### 4. BioSection (`bio_section.dart`)
- ইউজারের বায়ো এবং অতিরিক্ত তথ্য
- লোকেশন ইনফো
- শর্তাধীন রেন্ডারিং

### 5. ActionButtons (`action_buttons.dart`)
- এডিট প্রোফাইল, শেয়ার, সেটিংস বাটন
- নিজের এবং অন্যের প্রোফাইলের জন্য আলাদা বাটন
- আধুনিক বাটন ডিজাইন

### 6. <PERSON><PERSON><PERSON><PERSON> (`profile_container.dart`)
- সব কম্পোনেন্ট একসাথে সংগঠিত করে
- প্রোপ পাসিং এবং ইভেন্ট হ্যান্ডলিং
- মূল প্রোফাইল লেআউট

### 7. ProfileTabs (`profile_tabs.dart`)
- পোস্ট এবং প্রোডাক্ট ট্যাব
- গ্রিড ভিউ লেআউট
- এম্পটি স্টেট হ্যান্ডলিং

### 8. ProfileAppBar (`profile_app_bar.dart`)
- কাস্টম অ্যাপ বার
- গ্রেডিয়েন্ট ব্যাকগ্রাউন্ড
- অ্যাকশন বাটন এবং মেনু

## ব্যবহার

```dart
import '../widgets/profile/index.dart';

// প্রোফাইল কন্টেইনার ব্যবহার
ProfileContainer(
  user: user,
  postsCount: postsCount,
  isOwnProfile: true,
  onEditProfile: () => {},
  // অন্যান্য কলব্যাক...
)
```

## বৈশিষ্ট্য

- ✅ কম্পোনেন্ট-বেসড আর্কিটেকচার
- ✅ পুনঃব্যবহারযোগ্য কোড
- ✅ আধুনিক UI ডিজাইন
- ✅ রেসপন্সিভ লেআউট
- ✅ হ্যাপটিক ফিডব্যাক
- ✅ লোডিং স্টেট
- ✅ এরর হ্যান্ডলিং
- ✅ এম্পটি স্টেট
- ✅ পুল-টু-রিফ্রেশ

## ডিজাইন প্রিন্সিপাল

1. **মডুলারিটি**: প্রতিটি কম্পোনেন্ট একটি নির্দিষ্ট দায়িত্ব পালন করে
2. **পুনঃব্যবহারযোগ্যতা**: কম্পোনেন্টগুলি বিভিন্ন জায়গায় ব্যবহার করা যায়
3. **কনসিস্টেন্সি**: সব কম্পোনেন্টে একই ডিজাইন ভাষা
4. **পারফরমেন্স**: অপ্টিমাইজড রেন্ডারিং এবং মেমোরি ব্যবহার
