import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../models/user_model.dart';
import 'profile_header.dart';
import 'statistics_card.dart';
import 'action_buttons.dart';

class ProfileContainer extends StatelessWidget {
  final UserModel? user;
  final int postsCount;
  final bool isOwnProfile;
  final VoidCallback? onEditCoverPhoto;
  final VoidCallback? onEditProfilePhoto;
  final VoidCallback? onProfilePhotoTap;
  final VoidCallback? onEditProfile;
  final VoidCallback? onShare;
  final VoidCallback? onSettings;
  final VoidCallback? onFollowersPressed;
  final VoidCallback? onFollowingPressed;
  final VoidCallback? onPostsPressed;

  const ProfileContainer({
    super.key,
    this.user,
    this.postsCount = 0,
    this.isOwnProfile = true,
    this.onEditCoverPhoto,
    this.onEditProfilePhoto,
    this.onProfilePhotoTap,
    this.onEditProfile,
    this.onShare,
    this.onSettings,
    this.onFollowersPressed,
    this.onFollowingPressed,
    this.onPostsPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Profile Header with Cover, Profile Photo and User Info
        ProfileHeader(
          user: user,
          onEditCoverPhoto: onEditCoverPhoto,
          onEditProfilePhoto: onEditProfilePhoto,
          onProfilePhotoTap: onProfilePhotoTap,
        ),

        const SizedBox(height: 16),

        // Statistics and Action Buttons Container
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: AppConstants.surfaceColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Statistics Card
                StatisticsCard(
                  user: user,
                  postsCount: postsCount,
                  onFollowersPressed: onFollowersPressed,
                  onFollowingPressed: onFollowingPressed,
                  onPostsPressed: onPostsPressed,
                ),

                const SizedBox(height: 16),

                // Action Buttons
                ActionButtons(
                  isOwnProfile: isOwnProfile,
                  onEditProfile: onEditProfile,
                  onShare: onShare,
                  onSettings: onSettings,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
