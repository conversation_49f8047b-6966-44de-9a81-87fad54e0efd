import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_constants.dart';

class ActionButtons extends StatelessWidget {
  final VoidCallback? onEditProfile;
  final VoidCallback? onShare;
  final VoidCallback? onSettings;
  final bool isOwnProfile;

  const ActionButtons({
    super.key,
    this.onEditProfile,
    this.onShare,
    this.onSettings,
    this.isOwnProfile = true,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (isOwnProfile) ...[
          Expanded(
            child: _buildEditProfileButton(),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildAddStoryButton(),
          ),
          const SizedBox(width: 8),
          _buildMoreButton(),
        ] else ...[
          Expanded(
            child: _buildFollowButton(),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildMessageButton(),
          ),
          const SizedBox(width: 8),
          _buildMoreButton(),
        ],
      ],
    );
  }

  Widget _buildEditProfileButton() {
    return ElevatedButton(
      onPressed: () {
        HapticFeedback.lightImpact();
        onEditProfile?.call();
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 0,
      ),
      child: const Text(
        'Edit profile',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildAddStoryButton() {
    return ElevatedButton(
      onPressed: () {
        HapticFeedback.lightImpact();
        // TODO: Implement add story functionality
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppConstants.backgroundColor,
        foregroundColor: AppConstants.textPrimaryColor,
        padding: const EdgeInsets.symmetric(vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 0,
      ),
      child: const Text(
        'Add story',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildFollowButton() {
    return ElevatedButton(
      onPressed: () {
        HapticFeedback.lightImpact();
        // TODO: Implement follow functionality
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 0,
      ),
      child: const Text(
        'Follow',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildMessageButton() {
    return ElevatedButton(
      onPressed: () {
        HapticFeedback.lightImpact();
        // TODO: Implement message functionality
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppConstants.backgroundColor,
        foregroundColor: AppConstants.textPrimaryColor,
        padding: const EdgeInsets.symmetric(vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 0,
      ),
      child: const Text(
        'Message',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildMoreButton() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: () {
          HapticFeedback.lightImpact();
          _showMoreOptions();
        },
        icon: const Icon(
          Icons.more_horiz,
          color: AppConstants.textPrimaryColor,
          size: 20,
        ),
        tooltip: 'More',
      ),
    );
  }

  void _showMoreOptions() {
    // TODO: Implement more options menu
    if (isOwnProfile) {
      onSettings?.call();
    } else {
      onShare?.call();
    }
  }
}
